<template>
  <BasicPopup v-bind="$attrs" title="详情" :showOkBtn="false" cancelText="关闭" @register="registerPopup" class="full-popup" @close="handleClose">
    <div class="setting-box p-20px">
      <div class="left-box">
        <h2 class="title mt-10px">困难生公示</h2>
        <hr class="mt-10px mb-10px" />
        <div class="sub-title mb-20px">
          <div v-if="fieldValue.sfgsrs">获奖人数：{{ fieldValue.allCount || 0 }}人</div>
          <div v-if="fieldValue.sfgsrq">公式日期：{{ fieldValue.gssj }}</div>
          <div v-if="fieldValue.sfllcs">浏览次数：{{ fieldValue.llcs || 0 }} 次</div>
        </div>
        <BasicTable @register="registerTable" v-if="fieldValue.type == 0"> </BasicTable>
        <template v-else>
          <div class="content-box mt-20px">
            <div class="text-content" v-for="(item, index) in fieldValue.content" :key="item.pddjmc">
              <div class="award-level"
                >{{ item.pddjmc }}获得者（<span>{{ item.num }}人</span>）：</div
              >
              <div class="name-list">
                <template v-for="child in item.children" :key="child.xm">
                  <span>{{ child.xm }}（{{ child.xsbh }}）</span>
                </template>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, toRefs, nextTick, watch, unref } from 'vue';
  import { BasicPopup, usePopup, usePopupInner } from '@/components/Popup';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ScrollContainer } from '@/components/Container';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/kns/gslljl');
  const { createMessage, createConfirm } = useMessage();
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const selectedOption = ref(1);
  const fieldValue = reactive({
    sfgsrs: 1,
    sfgsrq: 1,
    sfllcs: 1,
    nrxs: [],
  });

  // 表格选项配置
  const tableOptions = [
    { fullName: '姓名', id: 'xm' },
    { fullName: '学号', id: 'xsbh' },
    { fullName: '评定等级', id: 'djmc' },
    { fullName: '学院', id: 'dwmc' },
    { fullName: '专业', id: 'zymc' },
    { fullName: '班级', id: 'bjmc' },
  ];

  // 根据options生成columns的方法
  const generateColumns = (options: any[]): BasicColumn[] => {
    return options.map(option => ({
      title: option.fullName,
      dataIndex: option.id,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue?.nrxs?.includes(option.id);
      },
    }));
  };

  const columns: BasicColumn[] = generateColumns(tableOptions);
  const state = reactive({
    type: '0',
  });

  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys, setTableData }] = useTable({
    dataSource: [],
    columns,
    showTableSetting: false,
  });
  const [registerForm, { setFieldsValue, resetFields, validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'type',
        label: '内容显示',
        component: 'Radio',
        componentProps: {
          options: [
            {
              fullName: '列表式',
              id: '0',
            },
            {
              fullName: '文本式',
              id: '1',
            },
          ],
          onChange: async (e, value) => {
            fieldValue.type = e;
            if (e == '1') {
              setTimeout(() => {
                setColumns(generateColumns(tableOptions));
              }, 1000);
            }
          },
        },
      },
      {
        ifShow: () => fieldValue.type == '0',
        field: 'xszd',
        label: '内容显示',
        component: 'Checkbox',
        componentProps: {
          options: tableOptions,
          onChange: async (e, value) => {
            fieldValue.nrxs = e;
            if (fieldValue.type == '1') {
              setColumns(generateColumns(tableOptions));
            }
          },
        },
      },
      {
        field: 'sfllcs',
        label: '浏览次数',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.sfllcs = e;
          },
        },
      },
      {
        field: 'sfgsrs',
        label: '评定人数',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.sfgsrs = e;
          },
        },
      },
      {
        field: 'sfgsrq',
        label: '公示日期',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.sfgsrq = e;
          },
        },
      },
    ],
    layout: 'vertical',
  });
  const emit = defineEmits(['register', 'reload']);

  /**
   * 初始化函数
   *
   * @param data 初始化数据
   */
  async function init(data) {
    try {
      const res = await api.request('get', '/getAnnouncementInfo', {
        params: { knbzdm: data.id, dwdm: data.dwdm },
      });

      if (res.data) {
        const { config, list } = res.data;
        config.xszd = !config.xszd ? [] : JSON.parse(config.xszd);

        fieldValue.nrxs = config.xszd;
        fieldValue.type = config.type;
        fieldValue.sfgsrq = config.sfgsrq;
        fieldValue.sfgsrs = config.sfgsrs;
        fieldValue.sfllcs = config.sfllcs;

        fieldValue.allCount = res?.data?.allCount;
        fieldValue.gssj = res?.data?.gssj;
        fieldValue.llcs = res?.data?.llcs;

        fieldValue.list = list;
        nextTick(() => {
          setColumns(generateColumns(tableOptions));
          setTableData(list);
        });
      }
    } catch (error) {
      console.error('获取公示详情失败:', error);
      createMessage.error('获取公示详情失败');
    }
  }

  const handleClose = () => {
    closePopup();
  };
</script>
<style scoped lang="less">
  .setting-box {
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;

    .right-box {
      padding: 0 20px;
      border-left: 1px solid #dddddd;
    }
    .left-box {
      height: calc(100% - 40px);
      .content-box {
        display: flex;
        flex-direction: column;
        gap: 20px;
        .text-content {
          margin-bottom: 16px;
          .award-level {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
            span {
              font-size: 16px;
              color: #1890ff;
              font-weight: 600;
            }
          }
          .name-list {
            padding-left: 20px;
            line-height: 1.8;
            span {
              display: inline-block;
              margin-right: 20px;
              color: #666;
              font-size: 14px;
            }
          }
        }
      }

      .title {
        font-size: 20px;
        font-weight: 500;
        text-align: center;
      }
      .sub-title {
        display: grid;
        grid-template-columns: 1fr 3fr 1fr;
        gap: 20px;
        text-align: center;
      }
    }
  }
</style>
