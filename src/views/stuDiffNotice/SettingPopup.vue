<template>
  <BasicPopup
    v-bind="$attrs"
    title="公示模板设置"
    showOkBtn
    okText="保存"
    cancelText="关闭"
    @register="registerPopup"
    class="full-popup"
    @close="handleClose"
    @ok="handleSubmit">
    <div class="setting-box p-20px">
      <div class="left-box">
        <h2 class="title mt-10px">困难生公示</h2>
        <hr class="mt-10px mb-10px" />
        <div class="sub-title mb-20px">
          <div v-if="fieldValue.sfgsrs">获奖人数：6人</div>
          <div v-if="fieldValue.sfgsrq">公式日期：2025.05.01~2025.10.01</div>
          <div v-if="fieldValue.sfllcs">浏览次数：100次</div>
        </div>
        <BasicTable @register="registerTable" v-if="fieldValue.type == 0"> </BasicTable>
        <template v-else>
          <div class="content-box mt-20px">
            <div class="text-content">
              <div class="award-level">一等困难生获得者（<span>3人</span>）：</div>
              <div class="name-list">
                <span>张三（2021001001）</span>
                <span>赵六（2021004001）</span>
                <span>李明（2021006001）</span>
              </div>
            </div>
            <div class="text-content">
              <div class="award-level">二等困难生获得者（<span>2人</span>）：</div>
              <div class="name-list">
                <span>李四（2021002001）</span>
                <span>钱七（2021005001）</span>
              </div>
            </div>
            <div class="text-content">
              <div class="award-level">三等困难生获得者（<span>1人</span>）：</div>
              <div class="name-list">
                <span>王五（2021003001）</span>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="right-box">
        <div class="point mb-10px">公式页面信息设置</div>
        <BasicForm @register="registerForm"> </BasicForm>
      </div>
    </div>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, toRefs, nextTick, watch, unref } from 'vue';
  import { BasicPopup, usePopup, usePopupInner } from '@/components/Popup';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ScrollContainer } from '@/components/Container';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/kns/gssz');
  const { createMessage, createConfirm } = useMessage();
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const selectedOption = ref(1);
  const fieldValue = reactive({
    sfgsrs: 1,
    sfgsrq: 1,
    sfllcs: 1,
    nrxs: [],
  });

  // 表格选项配置
  const tableOptions = [
    { fullName: '姓名', id: 'xm' },
    { fullName: '学号', id: 'xsbh' },
    { fullName: '评定等级', id: 'pddjmc' },
    { fullName: '学院', id: 'dwmc' },
    { fullName: '专业', id: 'zymc' },
    { fullName: '班级', id: 'bjmc' },
  ];

  // 根据options生成columns的方法
  const generateColumns = (options: any[]): BasicColumn[] => {
    return options.map(option => ({
      title: option.fullName,
      dataIndex: option.id,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue?.nrxs?.includes(option.id);
      },
    }));
  };

  const columns: BasicColumn[] = generateColumns(tableOptions);
  const state = reactive({
    type: '0',
  });

  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    dataSource: [
      { pddjmc: '一等奖', dwmc: '计算机学院', xsbh: '2021001001', xm: '张三', zymc: '计算机科学与技术', bjmc: '计科2101班' },
      { pddjmc: '二等奖', dwmc: '电子工程学院', xsbh: '2021002001', xm: '李四', zymc: '电子信息工程', bjmc: '电信2101班' },
      { pddjmc: '三等奖', dwmc: '机械工程学院', xsbh: '2021003001', xm: '王五', zymc: '机械设计制造及其自动化', bjmc: '机械2101班' },
      { pddjmc: '一等奖', dwmc: '外国语学院', xsbh: '2021004001', xm: '赵六', zymc: '英语', bjmc: '英语2101班' },
      { pddjmc: '二等奖', dwmc: '经济管理学院', xsbh: '2021005001', xm: '钱七', zymc: '工商管理', bjmc: '工管2101班' },
    ],
    columns,
    showTableSetting: false,
  });
  const [registerForm, { setFieldsValue, resetFields, validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'type',
        label: '内容显示',
        component: 'Radio',
        componentProps: {
          options: [
            {
              fullName: '列表式',
              id: '0',
            },
            {
              fullName: '文本式',
              id: '1',
            },
          ],
          onChange: async (e, value) => {
            fieldValue.type = e;
            if (e == '1') {
              setTimeout(() => {
                setColumns(generateColumns(tableOptions));
              }, 1000);
            }
          },
        },
      },
      {
        ifShow: () => fieldValue.type == '0',
        field: 'xszd',
        label: '内容显示',
        component: 'Checkbox',
        componentProps: {
          options: tableOptions,
          onChange: async (e, value) => {
            fieldValue.nrxs = e;
            if (fieldValue.type == '1') {
              setColumns(generateColumns(tableOptions));
            }
          },
        },
      },
      {
        field: 'sfllcs',
        label: '浏览次数',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.sfllcs = e;
          },
        },
      },
      {
        field: 'sfgsrs',
        label: '评定人数',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.sfgsrs = e;
          },
        },
      },
      {
        field: 'sfgsrq',
        label: '公示日期',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.sfgsrq = e;
          },
        },
      },
    ],
    layout: 'vertical',
  });
  const emit = defineEmits(['register', 'reload']);

  /**
   * 初始化函数
   *
   * @param data 初始化数据
   */
  async function init(data) {
    try {
      const res = await api.request('get', '/getOne', { params: { id: data.id } });

      if (res.data) {
        const configData = res.data;
        configData.xszd = !configData.xszd ? [] : JSON.parse(configData.xszd);
        setFieldsValue(configData);
        fieldValue.nrxs = configData.xszd;
        fieldValue.type = configData.type;
        fieldValue.sfgsrq = configData.sfgsrq;
        fieldValue.sfgsrs = configData.sfgsrs;
        fieldValue.sfllcs = configData.sfllcs;
        nextTick(() => {
          setColumns(generateColumns(tableOptions));
        });
      }
    } catch (error) {
      console.error('获取公示设置失败:', error);
      createMessage.error('获取公示设置失败');
    }
  }
  async function handleSubmit() {
    try {
      const values = await validate();
      changeOkLoading(true);

      const submitData = {
        ...values,
        xszd: JSON.stringify(values.xszd || []),
      };

      const res = values.id ? await api.edit({ data: submitData, params: { id: values.id } }) : await api.save({ data: submitData });

      createMessage.success(res.msg || '保存成功');
      changeOkLoading(false);
      emit('reload');
      setTimeout(() => {
        closePopup();
      }, 200);
    } catch (error) {
      console.error('保存公示设置失败:', error);
      createMessage.error('保存公示设置失败');
      changeOkLoading(false);
    }
  }
  const handleClose = () => {
    closePopup();
  };
</script>
<style scoped lang="less">
  .setting-box {
    height: 100%;
    display: grid;
    grid-template-columns: minmax(100px, 1fr) 400px;
    gap: 20px;

    .right-box {
      padding: 0 20px;
      border-left: 1px solid #dddddd;
    }
    .left-box {
      height: calc(100% - 40px);
      .content-box {
        display: flex;
        flex-direction: column;
        gap: 20px;
        .text-content {
          margin-bottom: 16px;
          .award-level {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
            span {
              font-size: 16px;
              color: #1890ff;
              font-weight: 600;
            }
          }
          .name-list {
            padding-left: 20px;
            line-height: 1.8;
            span {
              display: inline-block;
              margin-right: 20px;
              color: #666;
              font-size: 14px;
            }
          }
        }
      }

      .title {
        font-size: 20px;
        font-weight: 500;
        text-align: center;
      }
      .sub-title {
        display: grid;
        grid-template-columns: 1fr 3fr 1fr;
        gap: 20px;
        text-align: center;
      }
    }
  }
</style>
